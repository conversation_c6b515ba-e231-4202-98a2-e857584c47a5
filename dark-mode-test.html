<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dark Mode Test</title>
    <link rel="stylesheet" href="./css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Dark Mode Visibility Test</h1>
        
        <!-- Profile Section Test -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2>Profile Section</h2>
                <div class="image-frame mx-auto my-3">
                    <div class="profile-image-container">
                        <img src="assets/images/profile-placeholder.svg" alt="Profile" class="profile-image rounded-circle">
                    </div>
                </div>
                <div class="profile-overlay mx-auto" style="position: relative; bottom: auto;">
                    <h3 class="profile-name">Daniel Kamunyu</h3>
                    <p class="profile-title">Web Developer & Graphic Designer</p>
                </div>
            </div>
        </div>

        <!-- Skills Section Test -->
        <div class="row mb-5">
            <div class="col-12">
                <h2 class="text-center mb-4">Skills Section</h2>
                <div class="col-md-6 mx-auto">
                    <div class="skill-item">
                        <div class="skill-info">
                            <p>HTML/CSS</p>
                            <p class="percent">90%</p>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 90%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Portfolio Filter Test -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="mb-4">Portfolio Filter</h2>
                <div class="portfolio-filter">
                    <button class="btn btn-filter active">All</button>
                    <button class="btn btn-filter">Graphic Design</button>
                    <button class="btn btn-filter">Web Design</button>
                </div>
            </div>
        </div>

        <!-- Contact Form Test -->
        <div class="row mb-5">
            <div class="col-md-6 mx-auto">
                <h2 class="text-center mb-4">Contact Form</h2>
                <form class="contact-form">
                    <div class="form-group mb-3">
                        <input type="text" class="form-control" placeholder="Your Name">
                    </div>
                    <div class="form-group mb-3">
                        <input type="email" class="form-control" placeholder="Your Email">
                    </div>
                    <div class="form-group mb-3">
                        <textarea class="form-control" rows="4" placeholder="Message"></textarea>
                    </div>
                    <button type="submit" class="btn btn-submit">Send Message</button>
                </form>
            </div>
        </div>

        <!-- Cards Test -->
        <div class="row mb-5">
            <div class="col-md-6 mx-auto">
                <h2 class="text-center mb-4">Contact Card</h2>
                <div class="custom-card text-center">
                    <div class="card-circle mx-auto">
                        <div class="card-circle-icon"><i class="fa fa-phone"></i></div>
                    </div>
                    <div class="card-heading">Contact Number</div>
                    <div class="card-para">+254725577645</div>
                </div>
            </div>
        </div>

        <!-- Theme Toggle Test -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="mb-4">Theme Toggle</h2>
                <div class="theme-switch-wrapper d-flex justify-content-center">
                    <i class="fas fa-moon slider-icon"></i>
                    <label class="theme-switch">
                        <input type="checkbox" id="theme-toggle-test" checked>
                        <span class="slider"></span>
                    </label>
                </div>
                <p class="mt-3">Toggle should switch between light and dark themes</p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="js/theme.js"></script>
    <script>
        // Simple theme toggle for testing
        document.getElementById('theme-toggle-test').addEventListener('change', function(e) {
            if (e.target.checked) {
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', 'light');
            }
        });
    </script>
</body>
</html>
