<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$" />
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="bootstrap" level="application" />
    <orderEntry type="library" name="font-awesome" level="application" />
    <orderEntry type="library" name="@popperjs/core" level="application" />
    <orderEntry type="library" name="jquery-3.4.1.slim" level="application" />
    <orderEntry type="library" name="popper.js" level="application" />
    <orderEntry type="library" name="font-awesome" level="application" />
    <orderEntry type="library" name="bootstrap-icons" level="application" />
    <orderEntry type="library" name="bootstrap" level="application" />
    <orderEntry type="library" name="@popperjs/core" level="application" />
    <orderEntry type="library" name="jquery-3.4.1.slim" level="application" />
    <orderEntry type="library" name="popper.js" level="application" />
  </component>
</module>