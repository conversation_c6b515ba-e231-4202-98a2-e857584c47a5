
# <PERSON> - Portfolio Website

A modern, responsive portfolio website showcasing web development and graphic design skills.

## 🚀 Features

### ✨ Modern Design
- **Responsive Layout**: Optimized for all devices (mobile, tablet, desktop)
- **Dark/Light Theme**: Toggle between themes with persistent storage
- **Loading Animation**: Smooth loading screen with branding
- **Typing Animation**: Dynamic text animation for professional titles
- **Smooth Scrolling**: Enhanced navigation experience

### 🎨 Interactive Elements
- **Portfolio Filtering**: Filter projects by category (All, Graphic Design, Web Design)
- **Hover Effects**: Engaging animations and transitions
- **Ripple Effects**: Modern button interactions
- **Skill Bars**: Animated progress bars on scroll
- **Scroll-to-Top**: Convenient navigation button

### 📱 User Experience
- **Fast Loading**: Optimized performance with lazy loading
- **Accessibility**: WCAG compliant with keyboard navigation
- **SEO Optimized**: Meta tags, structured data, and semantic HTML
- **Form Validation**: Real-time contact form validation
- **Cross-Browser**: Compatible with all modern browsers

## 🛠️ Technologies Used

- **HTML5**: Semantic markup and accessibility features
- **CSS3**: Modern styling with CSS variables and animations
- **JavaScript**: Interactive functionality and form handling
- **Bootstrap 5**: Responsive grid system and components
- **Font Awesome**: Professional icons
- **AOS Library**: Scroll animations
- **jQuery**: Enhanced DOM manipulation

## 📁 Project Structure

```
PORTFOLIO/
├── index.html              # Main HTML file
├── css/
│   └── style.css          # Main stylesheet
├── js/
│   ├── nav.js            # Navigation functionality
│   ├── theme.js          # Theme toggle functionality
│   └── portfolio.js      # Portfolio interactions
├── assets/
│   ├── images/           # Image assets
│   └── cv.pdf           # Resume/CV file
├── test-checklist.md     # Testing guidelines
└── README.md            # This file
```

## 🚀 Getting Started

1. **Clone or download** the repository
2. **Open** `index.html` in a web browser
3. **Customize** content in `index.html`
4. **Update** images in `assets/images/`
5. **Modify** styles in `css/style.css`

## 🎯 Customization Guide

### Personal Information
- Update name, title, and bio in `index.html`
- Replace profile image placeholder with actual photo
- Update social media links
- Replace CV file in `assets/` folder

### Portfolio Projects
- Add/remove portfolio items in the portfolio section
- Update project images in `assets/images/`
- Modify project descriptions and categories

### Styling
- Customize colors in CSS variables (`:root` section)
- Modify fonts, spacing, and animations
- Add custom themes or color schemes

### Contact Form
- Configure form action for backend processing
- Add email service integration (EmailJS, Formspree, etc.)
- Customize validation messages

## 📊 Performance Features

- **Lazy Loading**: Images load only when needed
- **Resource Preloading**: Critical resources loaded first
- **Optimized CSS**: Efficient delivery and minimal blocking
- **Throttled Events**: Smooth scroll performance
- **Compressed Assets**: Optimized file sizes

## ♿ Accessibility Features

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: ARIA labels and semantic HTML
- **Skip Navigation**: Quick access to main content
- **Color Contrast**: WCAG AA compliant colors
- **Focus Indicators**: Clear focus states

## 🔧 Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Feel free to fork this project and customize it for your own portfolio. If you find any bugs or have suggestions for improvements, please open an issue or submit a pull request.

## 📞 Contact

- **Email**: <EMAIL>
- **Phone**: +254725577645
- **LinkedIn**: [Daniel Mwangi](https://www.linkedin.com/in/daniel-mwangi-3b240a278/)

---

**Made with ❤️ by Daniel Kamunyu**
