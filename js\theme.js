// Theme toggle functionality
document.addEventListener('DOMContentLoaded', () => {
    const themeToggle = document.getElementById('theme-toggle');
    const themeToggleMobile = document.getElementById('theme-toggle-mobile');
    const themeIcon = document.querySelector('.slider-icon');
    const mobileThemeIcon = document.querySelector('.nav-item.navigation-list .slider-icon');
    
    // Check for saved theme preference or use default
    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    
    // Update toggle position based on current theme
    if (currentTheme === 'dark') {
        themeToggle.checked = true;
        themeToggleMobile.checked = true;
        themeIcon.classList.replace('fa-moon', 'fa-sun');
        mobileThemeIcon.classList.replace('fa-moon', 'fa-sun');
    }
    
    // Function to switch theme
    function switchTheme(e) {
        if (e.target.checked) {
            document.documentElement.setAttribute('data-theme', 'dark');
            localStorage.setItem('theme', 'dark');
            themeIcon.classList.replace('fa-moon', 'fa-sun');
            mobileThemeIcon.classList.replace('fa-moon', 'fa-sun');
        } else {
            document.documentElement.setAttribute('data-theme', 'light');
            localStorage.setItem('theme', 'light');
            themeIcon.classList.replace('fa-sun', 'fa-moon');
            mobileThemeIcon.classList.replace('fa-sun', 'fa-moon');
        }
    }
    
    // Add event listeners to both toggles
    themeToggle.addEventListener('change', switchTheme);
    themeToggleMobile.addEventListener('change', function(e) {
        themeToggle.checked = e.target.checked;
        switchTheme(e);
    });
});