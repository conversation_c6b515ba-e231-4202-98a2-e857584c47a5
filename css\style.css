/* CSS Variables */
:root {
    /* Light Theme Colors */
    --bg-color: #ffffff;
    --text-color: #000000;
    --navbar-bg: rgba(255, 255, 255, 0.8);
    --navbar-text: #000000;
    --button-bg: #2196F3;
    --button-text: #ffffff;
    --card-bg: #f8f9fa;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --section-bg: #f8f9fa;
    --section-alt-bg: #e9ecef;
    --transition: all 0.3s ease;
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-color: #121212;
    --text-color: #ffffff;
    --navbar-bg: rgba(33, 33, 33, 0.8);
    --navbar-text: #ffffff;
    --button-bg: #2196F3;
    --button-text: #ffffff;
    --card-bg: #1e1e1e;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    --section-bg: #1a1a1a;
    --section-alt-bg: #242424;
}

/* Global Styles */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: var(--transition);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

/* Skip Navigation Link */
.skip-nav {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--button-bg);
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 0 0 4px 4px;
    z-index: 10000;
    font-weight: 600;
    transition: top 0.3s;
}

.skip-nav:focus {
    top: 0;
    color: white;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--button-bg) 0%, #1976D2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.fade-out {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

.loading-text {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    animation: pulse 2s ease-in-out infinite;
}

.loading-subtitle {
    font-size: 16px;
    opacity: 0.8;
    margin-bottom: 0;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Theme Switch Styles */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.theme-switch {
    display: inline-block;
    position: relative;
    width: 50px;
    height: 24px;
    margin: 0 10px;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--button-bg);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider-icon {
    color: var(--navbar-text);
    font-size: 14px;
}

/* Navbar Styles */
.navbar {
    background-color: var(--navbar-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 15px 20px;
    transition: var(--transition);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.navbar-brand {
    color: var(--navbar-text);
    font-weight: 600;
    font-size: 1.5rem;
}

.nav-link {
    color: var(--navbar-text) !important;
    font-weight: 500;
    margin: 0 10px;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--button-bg) !important;
}

.navbar-toggler {
    border: none;
    outline: none;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Navigation scroll effects */
.navbar.sticky {
    background-color: rgba(0, 0, 0, 0.95) !important;
    backdrop-filter: blur(10px);
}

.color-white {
    color: white !important;
}

.nav-btn-icon {
    color: white !important;
}

.nav-link.active {
    color: var(--button-bg) !important;
    font-weight: 600;
}

/* Main Container */
.main {
    padding-top: 80px;
}

/* Section Styles */
section {
    padding: 60px 0;
    background-color: var(--bg-color);
    transition: var(--transition);
}

section:nth-child(even) {
    background-color: var(--section-bg);
}

.section-1, .section-3 {
    background-color: var(--section-alt-bg);
    position: relative;
    overflow: hidden;
}

/* Profile Image Section */
.image-frame {
    position: relative;
    width: 300px;
    height: 300px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image-container {
    position: relative;
    display: inline-block;
}

.profile-image {
    width: 250px;
    height: 250px;
    border: 5px solid var(--button-bg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
    object-fit: cover;
}

.profile-image:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.profile-overlay {
    position: absolute;
    bottom: -50px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    background: var(--card-bg);
    padding: 20px;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    min-width: 280px;
}

.profile-name {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--text-color);
}

.profile-title {
    font-size: 16px;
    color: var(--button-bg);
    margin-bottom: 15px;
    font-weight: 500;
    min-height: 24px;
}

.typing-text::after {
    content: '|';
    animation: blink 1s infinite;
    color: var(--button-bg);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

.profile-social {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.profile-social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: var(--button-bg);
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.profile-social-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
    color: white;
}

.primary-heading {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    transition: var(--transition);
}

.paragraph {
    color: var(--text-color);
    max-width: 800px;
    line-height: 1.8;
    transition: var(--transition);
    font-size: 18px;
}

/* About Features */
.about-feature {
    text-align: center;
    padding: 30px 20px;
    background-color: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    height: 100%;
    margin-bottom: 20px;
}

.about-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 48px;
    color: var(--button-bg);
    margin-bottom: 20px;
    display: block;
}

.about-feature h5 {
    color: var(--text-color);
    font-weight: 600;
    margin-bottom: 15px;
    font-size: 20px;
}

.about-feature p {
    color: var(--text-color);
    opacity: 0.8;
    line-height: 1.6;
    margin-bottom: 0;
}

/* Button Styles */
.btn {
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-color-2 {
    background-color: var(--button-bg);
    color: var(--button-text);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.btn-color-2:hover {
    background-color: transparent;
    border: 2px solid var(--button-bg);
    color: var(--text-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

/* Ripple Effect */
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Skills Section */
.skills-section {
    padding: 40px 0;
    margin-top: 30px;
}

.skills-heading {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 30px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.skills-heading::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--button-bg);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.skill-item {
    background-color: var(--card-bg);
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    transition: var(--transition);
    height: 100%;
}

.skill-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.skill-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.skill-info p {
    margin: 0;
    font-weight: 500;
    color: var(--text-color);
}

.percent {
    font-weight: 600;
}

.progress {
    height: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--button-bg) 0%, #4a4a4a 100%);
    transition: width 1.5s ease-in-out;
}

[data-theme="dark"] .progress {
    background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .progress-bar {
    background: linear-gradient(90deg, #2196F3 0%, #0d47a1 100%);
}

/* Dark mode specific fixes for better visibility */
[data-theme="dark"] .profile-overlay {
    background: var(--card-bg);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .about-feature {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .portfolio-item .card {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .custom-card {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .contact-form {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .section-7 {
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .form-control {
    background-color: var(--card-bg);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-color);
}

[data-theme="dark"] .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .form-control:focus {
    background-color: var(--card-bg);
    border-color: var(--button-bg);
    color: var(--text-color);
}

/* Additional dark mode improvements */
[data-theme="dark"] .navbar.sticky {
    background-color: rgba(18, 18, 18, 0.95) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .portfolio-overlay {
    background: linear-gradient(to top, rgba(18, 18, 18, 0.9), transparent);
}

[data-theme="dark"] .loading-screen {
    background: linear-gradient(135deg, #1976D2 0%, #0d47a1 100%);
}

[data-theme="dark"] .skill-info p {
    color: var(--text-color);
}

[data-theme="dark"] .btn-filter {
    border-color: var(--button-bg);
    color: var(--text-color);
}

[data-theme="dark"] .btn-filter:hover,
[data-theme="dark"] .btn-filter.active {
    background-color: var(--button-bg);
    color: white;
}

/* Ensure text visibility in dark mode */
[data-theme="dark"] .sec5-portfolio-head,
[data-theme="dark"] .section-6-heading,
[data-theme="dark"] .section-6-para,
[data-theme="dark"] .sec4-text,
[data-theme="dark"] .section-4-heading,
[data-theme="dark"] .section-4-heading-bold,
[data-theme="dark"] .text__light,
[data-theme="dark"] .sec3-btn-right,
[data-theme="dark"] .sec6-btn,
[data-theme="dark"] .footer-copyright {
    color: var(--text-color);
}

/* Dark mode button styles */
[data-theme="dark"] .sec3-btn-right {
    background-color: var(--button-bg);
    color: white;
    border: 2px solid var(--button-bg);
}

[data-theme="dark"] .sec3-btn-right:hover {
    background-color: transparent;
    color: var(--button-bg);
}

[data-theme="dark"] .sec6-btn {
    background-color: var(--button-bg);
    color: white;
    border: 2px solid var(--button-bg);
    text-decoration: none;
}

[data-theme="dark"] .sec6-btn:hover {
    background-color: transparent;
    color: var(--button-bg);
}

/* Dark mode section backgrounds */
[data-theme="dark"] .section-3 {
    background-color: var(--section-alt-bg);
}

[data-theme="dark"] .section-6 {
    background-color: var(--section-bg);
}

/* Portfolio Section */
.portfolio-section {
    padding: 60px 0;
}

.portfolio-heading {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.portfolio-heading::after {
    content: '';
    position: absolute;
    width: 50px;
    height: 3px;
    background-color: var(--button-bg);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

/* Portfolio Filter */
.portfolio-filter {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.btn-filter {
    background-color: transparent;
    border: 1px solid var(--button-bg);
    color: var(--text-color);
    margin: 0 5px;
    padding: 8px 15px;
    border-radius: 30px;
    font-size: 14px;
    transition: var(--transition);
}

.btn-filter:hover, .btn-filter.active {
    background-color: var(--button-bg);
    color: #fff;
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 30px;
    transition: var(--transition);
}

.portfolio-item .card {
    background-color: var(--card-bg);
    border: none;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: var(--transition);
}

.portfolio-item:hover .card {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.portfolio-img {
    height: 240px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.portfolio-item:hover .portfolio-img {
    transform: scale(1.05);
}

.portfolio-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    padding: 20px;
    color: #fff;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.portfolio-category {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 0;
}

/* Fix for View Website Designs work text color */
.sec5-portfolio-head {
    color: var(--text-color);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
    transition: var(--transition);
}

/* Section 7 - Social Connect */
.section-7 {
    background-color: var(--card-bg);
    padding: 60px 0;
    border-radius: 10px;
    margin: 40px auto;
    max-width: 1140px;
    box-shadow: var(--card-shadow);
}

.section-7-inner {
    max-width: 800px;
}

.section-7-heading {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
}

.section-7-heading::after {
    content: '';
    position: absolute;
    width: 40px;
    height: 3px;
    background-color: var(--button-bg);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.section-7-text {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 30px;
    color: var(--text-color);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.section-7-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.social-icons {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--button-bg);
    color: #fff;
    font-size: 20px;
    transition: var(--transition);
    transform: translateY(0);
}

.social-icons:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    background-color: var(--text-color);
    color: var(--card-bg);
}

/* Contact Section */
.contact-section {
    background-color: var(--section-bg);
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0) 100%);
    z-index: 0;
}

.contact-heading {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--text-color);
    position: relative;
    display: inline-block;
    z-index: 1;
}

.contact-heading::after {
    content: '';
    position: absolute;
    width: 60px;
    height: 4px;
    background-color: var(--button-bg);
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 2px;
}

.contact-para {
    color: var(--text-color);
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.8;
    z-index: 1;
    position: relative;
}

.custom-card {
    background-color: var(--card-bg);
    padding: 30px 20px;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    transition: all 0.4s ease;
    border: none;
    height: 100%;
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.custom-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card-circle {
    width: 80px;
    height: 80px;
    background-color: rgba(33, 150, 243, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    transition: all 0.4s ease;
}

.custom-card:hover .card-circle {
    background-color: var(--button-bg);
    transform: scale(1.1);
}

.card-circle-icon {
    font-size: 28px;
    color: var(--button-bg);
    transition: all 0.4s ease;
}

.custom-card:hover .card-circle-icon {
    color: white;
}

.card-heading {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
}

.card-para {
    color: var(--text-color);
    margin-bottom: 0;
}

.card-link {
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.card-link:hover {
    color: var(--button-bg);
}

.contact-form {
    background-color: var(--card-bg);
    padding: 40px;
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    transition: all 0.4s ease;
    position: relative;
    z-index: 1;
    margin: 0 auto;
    max-width: 90%;
}

.card-row-1 {
    margin-bottom: 50px;
}

.form-control {
    background-color: var(--bg-color);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--text-color);
    padding: 15px 20px;
    border-radius: 10px;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.form-control:focus {
    border-color: var(--button-bg);
    box-shadow: 0 0 0 0.2rem rgba(33, 150, 243, 0.25);
    transform: translateY(-2px);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

.btn-submit {
    background-color: var(--button-bg);
    color: white;
    padding: 12px 30px;
    border-radius: 30px;
    border: none;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    margin-top: 10px;
    position: relative;
    overflow: hidden;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(33, 150, 243, 0.3);
}

/* Form input animations */
@keyframes formFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.contact-form .form-control {
    animation: formFadeIn 0.5s ease forwards;
    opacity: 0;
}

.contact-form .form-control:nth-child(1) {
    animation-delay: 0.1s;
}

.contact-form .form-control:nth-child(2) {
    animation-delay: 0.2s;
}

.contact-form .form-control:nth-child(3) {
    animation-delay: 0.3s;
}

.contact-form .btn-submit {
    animation: formFadeIn 0.5s ease forwards;
    animation-delay: 0.4s;
    opacity: 0;
}

/* Footer */
footer {
    background-color: var(--section-alt-bg);
    padding: 30px 0;
    color: var(--text-color);
}

/* Responsive Adjustments */
@media (max-width: 1199.98px) {
    .image-frame {
        width: 280px;
        height: 280px;
    }

    .profile-image {
        width: 220px;
        height: 220px;
    }
}

@media (max-width: 991.98px) {
    .navbar {
        padding: 10px 15px;
    }

    .primary-heading {
        font-size: 2rem;
    }

    .image-frame {
        width: 250px;
        height: 250px;
    }

    .profile-image {
        width: 200px;
        height: 200px;
    }

    .profile-overlay {
        min-width: 250px;
        bottom: -40px;
    }
}

@media (max-width: 767.98px) {
    .section-1, .section-3 {
        padding: 40px 0;
    }

    .primary-heading {
        font-size: 1.8rem;
    }

    .portfolio-img {
        height: 200px;
    }

    .section-7-icons {
        gap: 15px;
    }

    .social-icons {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }

    .image-frame {
        width: 220px;
        height: 220px;
    }

    .profile-image {
        width: 180px;
        height: 180px;
    }

    .profile-overlay {
        min-width: 220px;
        bottom: -35px;
        padding: 15px;
    }

    .profile-name {
        font-size: 20px;
    }

    .about-feature {
        margin-bottom: 30px;
    }

    .feature-icon {
        font-size: 40px;
    }
}

@media (max-width: 575.98px) {
    .navbar-brand {
        font-size: 1.2rem;
    }

    .primary-heading {
        font-size: 1.5rem;
    }

    .btn {
        padding: 8px 20px;
    }

    .portfolio-img {
        height: 180px;
    }

    .section-7 {
        padding: 40px 15px;
    }

    .section-7-icons {
        flex-wrap: wrap;
        justify-content: center;
    }

    .image-frame {
        width: 200px;
        height: 200px;
    }

    .profile-image {
        width: 160px;
        height: 160px;
    }

    .profile-overlay {
        min-width: 200px;
        bottom: -30px;
        padding: 12px;
    }

    .profile-name {
        font-size: 18px;
    }

    .profile-title {
        font-size: 14px;
    }

    .profile-social-link {
        width: 30px;
        height: 30px;
        font-size: 14px;
    }

    .paragraph {
        font-size: 16px;
    }

    .contact-form {
        padding: 30px 20px;
    }
}