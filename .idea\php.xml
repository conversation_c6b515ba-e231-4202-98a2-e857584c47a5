<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="LaravelPint">
    <laravel_pint_settings>
      <laravel_pint_by_interpreter asDefaultInterpreter="true" interpreter_id="5860baa7-a384-40de-86b0-97c3572a387a">
        <option name="timeout" value="30000" />
      </laravel_pint_by_interpreter>
    </laravel_pint_settings>
  </component>
  <component name="MessDetector">
    <phpmd_settings>
      <phpmd_by_interpreter asDefaultInterpreter="true" interpreter_id="5860baa7-a384-40de-86b0-97c3572a387a" timeout="30000" />
    </phpmd_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCSFixer">
    <phpcsfixer_settings>
      <phpcs_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="5860baa7-a384-40de-86b0-97c3572a387a" timeout="30000" />
    </phpcsfixer_settings>
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <phpcs_by_interpreter asDefaultInterpreter="true" interpreter_id="5860baa7-a384-40de-86b0-97c3572a387a" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.4" />
  <component name="PhpStan">
    <PhpStan_settings>
      <phpstan_by_interpreter asDefaultInterpreter="true" interpreter_id="5860baa7-a384-40de-86b0-97c3572a387a" timeout="60000" />
    </PhpStan_settings>
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="Psalm">
    <Psalm_settings>
      <psalm_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="5860baa7-a384-40de-86b0-97c3572a387a" timeout="60000" />
    </Psalm_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>