<svg width="250" height="250" viewBox="0 0 250 250" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="125" cy="125" r="125" fill="url(#bgGradient)"/>
  <!-- Person icon -->
  <circle cx="125" cy="95" r="35" fill="rgba(255,255,255,0.9)"/>
  <path d="M70 180C70 155 94.5 135 125 135S180 155 180 180V200H70V180Z" fill="rgba(255,255,255,0.9)"/>
  <!-- Shimmer effect overlay -->
  <defs>
    <linearGradient id="shimmer" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
      <stop offset="50%" style="stop-color:rgba(255,255,255,0.3);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:0" />
    </linearGradient>
  </defs>
  <rect x="0" y="0" width="250" height="250" fill="url(#shimmer)" opacity="0.5">
    <animateTransform
      attributeName="transform"
      attributeType="XML"
      type="translate"
      values="-250 0; 250 0; -250 0"
      dur="2s"
      repeatCount="indefinite"/>
  </rect>
</svg>
