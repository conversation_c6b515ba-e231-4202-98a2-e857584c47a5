// Enhanced navigation functionality
$(document).ready(function() {
    // Navbar scroll effect
    $(window).on("scroll", function(){
        if($(window).scrollTop() > 86) {
            $("#navbar").addClass("sticky");
            $("#nav-logo").addClass("color-white");
            $(".nav-custom-link").addClass("color-white");
            $(".navbar-btn").addClass("nav-btn-icon");
        } else {
            $("#navbar").removeClass("sticky");
            $("#nav-logo").removeClass("color-white");
            $(".nav-custom-link").removeClass("color-white");
            $(".navbar-btn").removeClass("nav-btn-icon");
        }
    });

    // Active navigation link highlighting
    $(window).on('scroll', function() {
        var scrollPos = $(window).scrollTop() + 100;

        $('.nav-link[href^="#"]').each(function() {
            var currLink = $(this);
            var refElement = $(currLink.attr("href"));

            if (refElement.length && refElement.position().top <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                $('.nav-link').removeClass("active");
                currLink.addClass("active");
            } else {
                currLink.removeClass("active");
            }
        });
    });

    // Close mobile menu when clicking on a link
    $('.navbar-nav .nav-link').on('click', function() {
        $('.navbar-collapse').collapse('hide');
    });
});